import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { connectDisconnectToShipRocket } from '../utils';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import StoreConnectionSteps from './woocommerce-steps';
import CommerceIntegrationLayout from './commerce-integration-layout';
import Input from './Input';
import { AuthUser } from '../../../types/auth';
import image from '../images/integrations/Shiprocket-logo.png';
import { shiprocketIntegrationSteps } from '../utils/constant';
import endPoints from '../apis/agent';


interface FormFields {
   channelName: string;
   email: string;
   password: string;
}

interface ApiError {
   success: boolean;
   message: string;
}

const SellerPanel: React.FC<{
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   trying: boolean;
   onConnect: (e: React.FormEvent<HTMLFormElement>) => void;
   email: string;
   password: string;
   apiError: ApiError | null;
}> = ({ onChange, trying, onConnect, email, password, apiError }) => (
   <div className='seller-panel'>
      <div className='seller-panel__headings'>
         <h5 className='title'>Seller Panel</h5>
         <p className='description'>
            Please provide the following credentials for Shiprocket integration:
         </p>
      </div>
      <form className='seller-panel__form' onSubmit={onConnect}>
         <div className='inputs'>
            <Input
               type='email'
               id='email'
               label='Email'
               placeholder='Enter your email...'
               name='email'
               value={email}
               onChange={onChange}
            />
            <Input
               type='password'
               id='password'
               label='Password'
               placeholder='Enter your password...'
               name='password'
               value={password}
               onChange={onChange}
            />
         </div>
         {apiError && apiError.message && (
            <div
               className={`api-response ${apiError.success ? 'api-response__success' : 'api-response__error'}`}
            >
               <h3 className='api-response__message'>{apiError.message}</h3>
            </div>
         )}
         <button
            disabled={trying}
            className='commerce-integration__button'
            type='submit'
         >
            {trying ? 'Connecting...' : 'Connect'}
         </button>
      </form>
   </div>
);

const ShipRocketForm: React.FC = () => {
   const navigate = useNavigate();
   const [trying, setTrying] = useState<boolean>(false);
   const [apiError, setApiError] = useState<ApiError | null>(null);

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const [unmounted, setUnmounted] = useState<boolean>(false);

   const defaultState: FormFields = {
      channelName: 'shiprocket',
      email: '',
      password: '',
   };

   const [formFields, setFormFields] = useState<FormFields>(defaultState);

   const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
         if (unmounted) return;
         const { name, value } = e.target;
         setFormFields((prev) => ({ ...prev, [name]: value }));
      },
      [unmounted],
   );

   const {
      mutateAsync: validateShipRocketAcc,
      isPending: isValidating,
      errorMessage: validateShipRocketAccError,
   } = useApiMutation<{ success: boolean; message: string },{email:string,password:string}>({
      mutationFn: endPoints.validateShipRocketAcc,
   });
   const handleConnect = useCallback(
      (e: React.FormEvent<HTMLFormElement>) => {
         if (!client_id) return;
         e.preventDefault();
         if (unmounted) return;
         const { email, password } = formFields;
         void (async () => {
            try {
               setTrying(true);
               setApiError({
                  success: false,
                  message: '',
               });
              /* const res = await endPoints.({
                  email,
                  password,
               });
               */
               await connectDisconnectToShipRocket({
                  channel_name: 'shiprocket',
                  client_id,
                  email: email,
                  password: password,
                  isConnect: true,
               });
               setFormFields(defaultState);
               setApiError({
                  success: true,
                  message: 'Connection Established, Redirecting...',
               });
               setTimeout(() => {
                  navigate('/integrations');
               }, 3000);
            } catch (err) {
               let errMessage = 'Error connecting to shiprocket';
               if (
                  err &&
                  typeof err === 'object' &&
                  'response' in err &&
                  err.response &&
                  typeof err.response === 'object' &&
                  'data' in err.response &&
                  err.response.data &&
                  typeof err.response.data === 'object' &&
                  'message' in err.response.data
               ) {
                  errMessage =
                     (err.response.data as { message?: string }).message ||
                     errMessage;
               }
               setApiError({
                  success: false,
                  message: errMessage,
               });
            } finally {
               setTrying(false);
            }
         })();
      },
      [formFields, defaultState, unmounted],
   );

   useEffect(() => {
      setUnmounted(false);
      return () => {
         setUnmounted(true);
         setFormFields(defaultState);
         setApiError(null);
      };
   }, []);

   const leftContent = (
      <StoreConnectionSteps steps={shiprocketIntegrationSteps} />
   );

   return (
      <CommerceIntegrationLayout leftContent={leftContent} image={image}>
         <SellerPanel
            onChange={handleChange}
            trying={trying}
            onConnect={handleConnect}
            email={formFields.email}
            password={formFields.password}
            apiError={apiError}
         />
      </CommerceIntegrationLayout>
   );
};

export default ShipRocketForm;
