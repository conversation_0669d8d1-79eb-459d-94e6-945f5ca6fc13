import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
} from '@/components/ui/card';
import { useFetchCustomAlertOptions } from '../apis/custom-alerts-apis';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { useEffect, useState } from 'react';
import KpiRules from './kpi-rules';
import { MultiSelect } from '@/components/ui/multi-select';
import { Input } from '@/components/ui/input';
import SelectPeriodCombobox from './select-period-combobox';
import {
   CHANNEL_MAP,
   formConditionDescription,
   splitAndUppercase,
   TIME_PERIODS,
} from '../utils/custom-alerts/custom-alerts-helpers';
import { useAppDispatch, useAppSelector } from '@/store/store';
import { handleFormChange } from '@/store/reducer/custom-alerts-reducer';
import { KPIRules } from '../../../store/reducer/custom-alerts-reducer';
import { METRICS_MAP } from '../utils/custom-alerts/custom-alerts-constants';

interface FormState {
   channel: string;
   campaigns: { id: string; name: string }[];
   metrics: string[];
   targetPeriod: { value: string; label: string };
   referencePeriod: { value: string; label: string };
}

const ConditionBuilder = () => {
   const dispatch = useAppDispatch();

   const [form, setForm] = useState<FormState>({
      channel: '',
      campaigns: [],
      metrics: [],
      targetPeriod: { value: '', label: '' },
      referencePeriod: { value: '', label: '' },
   });
   const [conditionDescription, setConditionDescription] = useState<string>(
      'Please fill in all fields to preview when the alert will trigger.',
   );

   const { form: customAlertForm, editMode } = useAppSelector(
      (state) => state.customAlerts,
   );

   const {
      data: customAlertOptions,
      isFetching: isCustomAlertOptionsFetching,
   } = useFetchCustomAlertOptions();

   const handleSelectChange = (
      name: keyof FormState,
      value: string | { value: string; label: string },
   ) => {
      switch (name) {
         case 'channel':
            setForm((prev) => ({
               ...prev,
               [name]: value as string,
               campaigns: [],
               metrics: [],
            }));
            dispatch(
               handleFormChange({
                  name: 'channel',
                  value: value as string,
               }),
            );
            dispatch(handleFormChange({ name: 'campaigns', value: [] }));
            dispatch(handleFormChange({ name: 'metrics', value: [] }));
            dispatch(handleFormChange({ name: 'kpi_rules', value: [] }));
            break;
         case 'targetPeriod':
            setForm((prev) => ({
               ...prev,
               [name]: value as { value: string; label: string },
            }));
            dispatch(
               handleFormChange({
                  name: 'target_period',
                  value: (value as { value: string; label: string }) || {
                     value: '',
                     label: '',
                  },
               }),
            );
            break;
         case 'referencePeriod':
            setForm((prev) => ({
               ...prev,
               [name]: value as { value: string; label: string },
            }));
            dispatch(
               handleFormChange({
                  name: 'reference_period',
                  value: (value as { value: string; label: string }) || {
                     value: '',
                     label: '',
                  },
               }),
            );
            break;
      }
   };

   const handleCampaignChange = (value: string[]) => {
      const campaigns = customAlertOptions?.campaigns?.[
         form.channel === 'facebookads'
            ? 'meta_ads_campaigns'
            : 'google_ads_campaigns'
      ]?.filter((c: { id: string; name: string }) => value.includes(c.id));
      setForm((prevForm) => ({
         ...prevForm,
         campaigns: campaigns || [],
      }));

      if (
         customAlertForm.campaigns.length === 0 &&
         campaigns &&
         campaigns?.length > 0
      ) {
         dispatch(handleFormChange({ name: 'kpi_rules', value: [] }));
         dispatch(handleFormChange({ name: 'metrics', value: [] }));
         setForm((prevForm) => ({
            ...prevForm,
            metrics: [],
         }));
      }

      if (campaigns?.length === 0) {
         dispatch(handleFormChange({ name: 'kpi_rules', value: [] }));
         dispatch(handleFormChange({ name: 'metrics', value: [] }));
         setForm((prevForm) => ({
            ...prevForm,
            metrics: [],
         }));
      }
      dispatch(
         handleFormChange({
            name: 'campaigns',
            value: campaigns || [],
         }),
      );
   };

   const handleMetricChange = (value: string[]) => {
      setForm((prevForm) => ({
         ...prevForm,
         metrics: value,
      }));
      dispatch(
         handleFormChange({
            name: 'metrics',
            value: value,
         }),
      );

      const existingMetrics = customAlertForm.kpi_rules.map(
         (rule: KPIRules) => rule.metric,
      );

      if (existingMetrics.length > value.length) {
         const removedMetrics = existingMetrics.filter(
            (metric) => !value.includes(metric),
         );
         const updatedKpiRules = customAlertForm.kpi_rules.filter(
            (rule: KPIRules) => !removedMetrics.includes(rule.metric),
         );
         dispatch(
            handleFormChange({
               name: 'kpi_rules',
               value: updatedKpiRules,
            }),
         );
      } else {
         const newMetrics = value.filter(
            (metric) => !existingMetrics.includes(metric),
         );
         dispatch(
            handleFormChange({
               name: 'kpi_rules',
               value: [
                  ...customAlertForm.kpi_rules,
                  ...newMetrics.map(
                     (newMetric) =>
                        ({
                           metric: newMetric,
                           trend: '',
                           value: '',
                           value_type: 'absolute',
                           comparison: '',
                        }) as KPIRules,
                  ),
               ],
            }),
         );
      }
   };

   useEffect(() => {
      if (editMode) {
         setForm({
            channel: customAlertForm.channel,
            campaigns: customAlertForm.campaigns,
            metrics: customAlertForm.metrics,
            targetPeriod: customAlertForm.target_period,
            referencePeriod: customAlertForm.reference_period,
         });
      }
   }, [editMode]);

   useEffect(() => {
      setConditionDescription(formConditionDescription(customAlertForm));
   }, [customAlertForm]);

   return (
      <>
         <Card className='w-full border !border-gray-50 !shadow-sm'>
            <CardHeader className='w-full flex flex-row gap-1 items-center justify-between'>
               <CardDescription className='!text-base ml-2 font-bold text-muted-foreground text-green-700'>
                  {conditionDescription}
               </CardDescription>
            </CardHeader>
            <CardContent>
               {/* Row 1 - Channels, Campaigns, Metrics */}

               <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'>
                  <div className='col-span-1 flex flex-col gap-2'>
                     <p className='text-[14px] font-semibold ml-1'>
                        Channels<span className='text-red-500 ml-1'>*</span>
                     </p>
                     {!isCustomAlertOptionsFetching ? (
                        <Select
                           disabled={isCustomAlertOptionsFetching}
                           value={form.channel}
                           onValueChange={(val) =>
                              handleSelectChange('channel', val)
                           }
                        >
                           <SelectTrigger className='w-full !h-[40px] cursor-pointer focus-visible:ring-0 focus-visible:outline-none'>
                              <SelectValue placeholder='Select channel' />
                           </SelectTrigger>
                           <SelectContent className='bg-white'>
                              {customAlertOptions?.channels
                                 ?.filter((val) => val !== 'overall_metrics')
                                 .map((channel) => (
                                    <SelectItem
                                       key={channel}
                                       value={channel}
                                       className='cursor-pointer'
                                    >
                                       {
                                          CHANNEL_MAP[
                                             channel as keyof typeof CHANNEL_MAP
                                          ]
                                       }
                                    </SelectItem>
                                 ))}
                           </SelectContent>
                        </Select>
                     ) : (
                        <Input
                           id='channels-loading'
                           name='channels-loading'
                           className='!cursor-not-allowed h-[40px] focus-visible:ring-0 focus-visible:outline-none'
                           value='Loading please wait...'
                           readOnly
                        />
                     )}
                  </div>

                  <div className='col-span-1 flex flex-col gap-2'>
                     <p className='text-[14px] font-semibold ml-1'>Campaigns</p>
                     {(form.channel === 'facebookads' ||
                        form.channel === 'googleads') &&
                     customAlertOptions?.campaigns?.[
                        form.channel === 'facebookads'
                           ? 'meta_ads_campaigns'
                           : 'google_ads_campaigns'
                     ] &&
                     customAlertOptions?.campaigns?.[
                        form.channel === 'facebookads'
                           ? 'meta_ads_campaigns'
                           : 'google_ads_campaigns'
                     ].length > 0 ? (
                        <MultiSelect
                           options={
                              customAlertOptions?.campaigns?.[
                                 form.channel === 'facebookads'
                                    ? 'meta_ads_campaigns'
                                    : 'google_ads_campaigns'
                              ].map((campaign) => ({
                                 key: campaign.id,
                                 label: campaign.name,
                                 value: campaign.id,
                                 ...campaign,
                              })) || []
                           }
                           values={form.campaigns.map((c) => c.id)}
                           onValueChange={handleCampaignChange}
                           placeholder='Select Campaigns'
                           variant='inverted'
                           maxCount={0}
                        />
                     ) : isCustomAlertOptionsFetching ? (
                        <Input
                           id='campaigns-loading'
                           name='campaigns-loading'
                           className='!cursor-not-allowed h-[40px] focus-visible:ring-0 focus-visible:outline-none'
                           value='Loading please wait...'
                           readOnly
                        />
                     ) : (
                        <Input
                           id='no-campaigns'
                           name='no-campaigns'
                           className='!cursor-not-allowed h-[40px] focus-visible:ring-0 focus-visible:outline-none'
                           value='No campaigns available for this channel'
                           readOnly
                        />
                     )}
                  </div>

                  <div className='col-span-1 flex flex-col gap-2'>
                     <p className='text-[14px] font-semibold ml-1'>
                        Metrics<span className='text-red-500 ml-1'>*</span>
                     </p>
                     {form.channel ? (
                        <MultiSelect
                           options={
                              form.campaigns.length > 0
                                 ? customAlertOptions?.metrics?.[
                                      form?.channel === 'facebookads'
                                         ? 'meta_campaigns_metrics'
                                         : 'google_campaigns_metrics'
                                   ]
                                      ?.map((metric: string) => ({
                                         key: metric,
                                         label:
                                            METRICS_MAP?.[metric] ||
                                            splitAndUppercase(metric),
                                         value: metric,
                                      }))
                                      .sort((a, b) =>
                                         a.label.localeCompare(b.label),
                                      ) || []
                                 : customAlertOptions?.metrics?.dashboard_metrics?.[
                                      form.channel as keyof typeof customAlertOptions.metrics.dashboard_metrics
                                   ]
                                      ?.map((metric) => ({
                                         key: metric,
                                         label:
                                            METRICS_MAP?.[metric] ||
                                            splitAndUppercase(metric),
                                         value: metric,
                                      }))
                                      .sort((a, b) =>
                                         a.label.localeCompare(b.label),
                                      ) || []
                           }
                           values={form.metrics}
                           onValueChange={handleMetricChange}
                           placeholder='Select Metrics'
                           variant='inverted'
                           maxCount={0}
                        />
                     ) : isCustomAlertOptionsFetching ? (
                        <Input
                           id='metrics-loading'
                           name='metrics-loading'
                           className='!cursor-not-allowed h-[40px] focus-visible:ring-0 focus-visible:outline-none'
                           value='Loading please wait...'
                           readOnly
                        />
                     ) : (
                        <Input
                           id='no-metrics'
                           name='no-metrics'
                           className='!cursor-not-allowed h-[40px] focus-visible:ring-0 focus-visible:outline-none'
                           value='No options available'
                           readOnly
                        />
                     )}
                  </div>
               </div>

               {/* Row 2 - Current Period, Comparison Period */}

               <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
                  <div className='md:col-span-1 flex flex-col gap-2'>
                     <p className='text-[14px] font-semibold ml-1'>
                        Current Period
                        <span className='text-red-500 ml-1'>*</span>
                     </p>
                     <SelectPeriodCombobox
                        options={TIME_PERIODS}
                        selected={form.targetPeriod}
                        onSelect={(value: { value: string; label: string }) =>
                           handleSelectChange('targetPeriod', value)
                        }
                        placeholderInput='Select target period'
                     />
                  </div>
                  <div className='md:col-span-1 flex flex-col gap-2'>
                     <p className='text-[14px] font-semibold ml-1'>
                        Comparison Period
                        <span className='text-red-500 ml-1'>*</span>
                     </p>
                     <SelectPeriodCombobox
                        options={TIME_PERIODS}
                        selected={form.referencePeriod}
                        onSelect={(value: { value: string; label: string }) =>
                           handleSelectChange('referencePeriod', value)
                        }
                        placeholderInput='Select reference period'
                     />
                  </div>
               </div>

               {/* Row 3 - Metric, Trend, Value, Comparison */}

               {customAlertForm.metrics &&
                  customAlertForm.metrics.length > 0 && (
                     <div className='grid grid-cols-1 mt-6 mb-2'>
                        <p className='text-[16px] font-bold ml-1 mb-2'>
                           KPI Rules<span className='text-red-500 ml-1'>*</span>
                        </p>
                     </div>
                  )}
               {customAlertForm.kpi_rules.map((rule) => (
                  <KpiRules
                     key={rule.metric}
                     metric={rule.metric}
                     trend={rule.trend}
                     value={rule.value}
                     value_type={rule.value_type}
                     comparison={rule.comparison}
                  />
               ))}
            </CardContent>
         </Card>
      </>
   );
};

export default ConditionBuilder;
